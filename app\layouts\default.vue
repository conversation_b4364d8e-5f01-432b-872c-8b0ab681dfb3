<template>
  <div class="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
    <!-- Main Content Area -->
    <div class="flex">
      <!-- Content Area (left side) -->
      <div class="flex-1 p-8">
        <slot />
      </div>

      <!-- Right Sidebar Menu -->
      <div class="w-80 bg-white shadow-2xl border-l border-slate-200">
        <div class="p-6">
          <!-- Header -->
          <div class="mb-8">
            <h2 class="text-2xl font-bold text-slate-800 mb-2">Dashboard</h2>
            <p class="text-slate-600 text-sm">Navigate through options</p>
          </div>

          <!-- Menu Component -->
          <Menu as="div" class="relative">
            <MenuButton class="w-full flex items-center justify-between px-4 py-3 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-lg shadow-lg hover:from-blue-700 hover:to-blue-800 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
              <span class="font-medium">Options Menu</span>
              <svg class="w-5 h-5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
              </svg>
            </MenuButton>

            <transition
              enter-active-class="transition duration-200 ease-out"
              enter-from-class="transform scale-95 opacity-0"
              enter-to-class="transform scale-100 opacity-100"
              leave-active-class="transition duration-150 ease-in"
              leave-from-class="transform scale-100 opacity-100"
              leave-to-class="transform scale-95 opacity-0"
            >
              <MenuItems class="absolute right-0 left-0 mt-2 bg-white rounded-xl shadow-xl border border-slate-200 py-2 focus:outline-none z-50">
                <MenuItem
                  v-for="link in links"
                  :key="link.href"
                  as="template"
                  v-slot="{ active }"
                >
                  <a
                    :href="link.href"
                    :class="[
                      'flex items-center px-4 py-3 text-sm font-medium transition-all duration-150',
                      active
                        ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-600'
                        : 'text-slate-700 hover:bg-slate-50'
                    ]"
                  >
                    <!-- Icons for each menu item -->
                    <component :is="getIcon(link.label)" class="w-5 h-5 mr-3" />
                    {{ link.label }}
                  </a>
                </MenuItem>
              </MenuItems>
            </transition>
          </Menu>

          <!-- Additional Info Section -->
          <div class="mt-8 p-4 bg-slate-50 rounded-lg border border-slate-200">
            <h3 class="text-sm font-semibold text-slate-800 mb-2">Quick Info</h3>
            <p class="text-xs text-slate-600">Access all your dashboard features from this menu.</p>
          </div>

          <!-- Footer -->
          <div class="mt-8 pt-6 border-t border-slate-200">
            <p class="text-xs text-slate-500 text-center">© 2024 Dashboard</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { Menu, MenuButton, MenuItems, MenuItem } from '@headlessui/vue'
import { h } from 'vue'

const links = [
  { href: '/', label: 'Home' },
  { href: '/support', label: 'Support' },
  { href: '/license', label: 'License' },
  { href: '/sign-out', label: 'Sign out' },
]

// Function to get appropriate icon for each menu item
const getIcon = (label) => {
  const iconMap = {
    'Home': () => h('svg', {
      fill: 'none',
      stroke: 'currentColor',
      viewBox: '0 0 24 24',
      class: 'w-5 h-5'
    }, [
      h('path', {
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        'stroke-width': '2',
        d: 'm3 12 2-2m0 0 7-7 7 7M5 10v10a1 1 0 0 0 1 1h3m10-11 2 2m-2-2v10a1 1 0 0 1-1 1h-3m-6 0a1 1 0 0 0 1-1v-4a1 1 0 0 1 1-1h2a1 1 0 0 1 1 1v4a1 1 0 0 0 1 1m-6 0h6'
      })
    ]),
    'Support': () => h('svg', {
      fill: 'none',
      stroke: 'currentColor',
      viewBox: '0 0 24 24',
      class: 'w-5 h-5'
    }, [
      h('path', {
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        'stroke-width': '2',
        d: 'M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z'
      })
    ]),
    'License': () => h('svg', {
      fill: 'none',
      stroke: 'currentColor',
      viewBox: '0 0 24 24',
      class: 'w-5 h-5'
    }, [
      h('path', {
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        'stroke-width': '2',
        d: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z'
      })
    ]),
    'Sign out': () => h('svg', {
      fill: 'none',
      stroke: 'currentColor',
      viewBox: '0 0 24 24',
      class: 'w-5 h-5'
    }, [
      h('path', {
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        'stroke-width': '2',
        d: 'M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1'
      })
    ])
  }

  return iconMap[label] || (() => h('div', { class: 'w-5 h-5' }))
}
</script>